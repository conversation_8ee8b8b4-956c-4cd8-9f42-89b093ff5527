/* ملف CSS إضافي للتطبيق */

.app-container {
  animation: fadeIn 0.8s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.welcome-card {
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.logo {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 8px 20px rgba(212, 175, 55, 0.3);
  }
  50% {
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.5);
  }
  100% {
    box-shadow: 0 8px 20px rgba(212, 175, 55, 0.3);
  }
}

.verse-container {
  animation: fadeInUp 0.8s ease-out 0.3s both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-button {
  animation: fadeInUp 0.8s ease-out 0.5s both;
}

.qr-section {
  animation: fadeInUp 0.8s ease-out 0.7s both;
}

.footer {
  animation: fadeInUp 0.8s ease-out 0.9s both;
}
