<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ملكوتك آتٍ - الصفحة الرئيسية</title>
    <link rel="icon" type="image/svg+xml" href="public/logo.svg">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-gold: #D4AF37;
            --primary-dark: #B8860B;
            --secondary-blue: #1E3A8A;
            --text-dark: #1F2937;
            --text-light: #6B7280;
            --white: #FFFFFF;
            --background: #f8fafc;
            --shadow: rgba(0, 0, 0, 0.1);
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: var(--background);
            min-height: 100vh;
            direction: rtl;
            color: var(--text-dark);
        }

        .header {
            background: linear-gradient(135deg, var(--primary-gold), var(--primary-dark));
            color: var(--white);
            padding: 20px 0;
            box-shadow: 0 4px 12px var(--shadow);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo-mini {
            width: 50px;
            height: 50px;
            background: var(--white);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logo-mini i {
            font-size: 24px;
            color: var(--primary-gold);
        }

        .app-info h1 {
            font-family: 'Amiri', serif;
            font-size: 1.8rem;
            margin-bottom: 5px;
        }

        .app-info p {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .user-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-info {
            text-align: left;
        }

        .user-email {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            color: var(--white);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            padding: 8px 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .main-content {
            max-width: 1200px;
            margin: 40px auto;
            padding: 0 20px;
        }

        .welcome-section {
            background: var(--white);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 8px 25px var(--shadow);
            margin-bottom: 30px;
        }

        .welcome-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--primary-gold), var(--primary-dark));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
        }

        .welcome-icon i {
            font-size: 40px;
            color: var(--white);
        }

        .welcome-title {
            font-family: 'Amiri', serif;
            font-size: 2rem;
            color: var(--secondary-blue);
            margin-bottom: 15px;
        }

        .welcome-text {
            font-size: 1.1rem;
            color: var(--text-light);
            line-height: 1.6;
        }

        .coming-soon {
            background: linear-gradient(135deg, #e0f2fe, #b3e5fc);
            border: 2px dashed #29b6f6;
            border-radius: 16px;
            padding: 30px;
            text-align: center;
            margin-top: 30px;
        }

        .coming-soon i {
            font-size: 48px;
            color: #1976d2;
            margin-bottom: 15px;
        }

        .coming-soon h3 {
            font-family: 'Amiri', serif;
            font-size: 1.5rem;
            color: var(--secondary-blue);
            margin-bottom: 10px;
        }

        .coming-soon p {
            color: var(--text-light);
            font-size: 1rem;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
            
            .user-section {
                flex-direction: column;
                gap: 10px;
            }
            
            .welcome-section {
                padding: 30px 20px;
            }
            
            .welcome-title {
                font-size: 1.6rem;
            }
            
            .main-content {
                padding: 0 15px;
            }
        }
    </style>
</head>
<body>
    <!-- الهيدر -->
    <header class="header">
        <div class="header-content">
            <div class="logo-section">
                <div class="logo-mini">
                    <i class="fas fa-cross"></i>
                </div>
                <div class="app-info">
                    <h1>ملكوتك آتٍ</h1>
                    <p>Kingdom Coming</p>
                </div>
            </div>
            
            <div class="user-section">
                <div class="user-info">
                    <div class="user-email" id="userEmail">مرحباً بك</div>
                </div>
                <button class="logout-btn" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i>
                    خروج
                </button>
            </div>
        </div>
    </header>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <div class="welcome-section">
            <div class="welcome-icon">
                <i class="fas fa-heart"></i>
            </div>
            <h2 class="welcome-title">مرحباً بك في ملكوتك آتٍ</h2>
            <p class="welcome-text">
                نحن سعداء بانضمامك إلينا في هذه الرحلة الروحية المباركة.<br>
                هذا هو بداية طريقك نحو النمو الروحي والتقرب من الله.
            </p>
        </div>

        <div class="coming-soon">
            <i class="fas fa-cogs"></i>
            <h3>قريباً...</h3>
            <p>نحن نعمل بجد لإضافة المزيد من المميزات الرائعة لتطبيقك المفضل</p>
        </div>
    </main>

    <script>
        // التحقق من تسجيل الدخول
        function checkLogin() {
            const isLoggedIn = localStorage.getItem('isLoggedIn');
            const userEmail = localStorage.getItem('userEmail');
            
            if (!isLoggedIn || isLoggedIn !== 'true') {
                window.location.href = 'login.html';
                return;
            }
            
            // عرض إيميل المستخدم
            if (userEmail) {
                document.getElementById('userEmail').textContent = userEmail;
            }
        }

        // تسجيل الخروج
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                localStorage.removeItem('isLoggedIn');
                localStorage.removeItem('userEmail');
                window.location.href = 'index.html';
            }
        }

        // تشغيل التحقق عند تحميل الصفحة
        window.onload = checkLogin;
    </script>
</body>
</html>
