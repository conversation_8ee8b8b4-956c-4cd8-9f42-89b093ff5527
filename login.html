<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - ملكوتك آتٍ</title>
    <link rel="icon" type="image/svg+xml" href="public/logo.svg">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-gold: #D4AF37;
            --primary-dark: #B8860B;
            --secondary-blue: #1E3A8A;
            --text-dark: #1F2937;
            --text-light: #6B7280;
            --white: #FFFFFF;
            --shadow: rgba(0, 0, 0, 0.1);
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            direction: rtl;
        }

        .login-container {
            background: var(--white);
            border-radius: 24px;
            padding: 40px 30px;
            box-shadow: 0 20px 40px var(--shadow);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }

        .back-button {
            position: absolute;
            top: 20px;
            right: 20px;
            background: var(--primary-gold);
            color: var(--white);
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            font-size: 18px;
            transition: all 0.3s ease;
        }

        .back-button:hover {
            background: var(--primary-dark);
            transform: scale(1.1);
        }

        .logo {
            width: 60px;
            height: 60px;
            margin: 0 auto 20px;
            background: linear-gradient(135deg, var(--primary-gold), var(--primary-dark));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logo i {
            font-size: 30px;
            color: var(--white);
        }

        .login-title {
            font-family: 'Amiri', serif;
            font-size: 1.8rem;
            color: var(--secondary-blue);
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: right;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: var(--text-dark);
            font-weight: 600;
        }

        .form-group input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
            direction: rtl;
            text-align: right;
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--primary-gold);
        }

        .login-btn {
            width: 100%;
            background: linear-gradient(135deg, var(--primary-gold), var(--primary-dark));
            color: var(--white);
            border: none;
            border-radius: 12px;
            padding: 15px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 10px;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(212, 175, 55, 0.4);
        }

        .forgot-password {
            margin-top: 20px;
            color: var(--text-light);
            text-decoration: none;
            font-size: 0.9rem;
        }

        .forgot-password:hover {
            color: var(--primary-gold);
        }

        .info-message {
            background: linear-gradient(135deg, #e0f2fe, #b3e5fc);
            border: 1px solid #29b6f6;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
            text-align: center;
        }

        .info-message i {
            font-size: 24px;
            color: #1976d2;
            margin-bottom: 10px;
        }

        .info-message p {
            margin: 5px 0;
            color: #1565c0;
        }

        .email-display {
            font-weight: 600;
            color: var(--primary-gold) !important;
            font-size: 0.95rem;
        }

        .resend-section {
            margin-top: 20px;
            text-align: center;
        }

        .resend-section p {
            color: var(--text-light);
            font-size: 0.9rem;
            margin-bottom: 10px;
        }

        .resend-btn {
            background: transparent;
            color: var(--primary-gold);
            border: 2px solid var(--primary-gold);
            border-radius: 8px;
            padding: 8px 16px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .resend-btn:hover {
            background: var(--primary-gold);
            color: var(--white);
        }

        .resend-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        #verificationCode {
            text-align: center;
            font-size: 1.2rem;
            letter-spacing: 2px;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <button class="back-button" onclick="goBack()">
        <i class="fas fa-arrow-right"></i>
    </button>

    <div class="login-container">
        <div class="logo">
            <i class="fas fa-cross"></i>
        </div>
        
        <h2 class="login-title">تسجيل الدخول</h2>
        
        <!-- مرحلة إدخال الإيميل -->
        <div id="emailStep">
            <form onsubmit="sendVerificationCode(event)">
                <div class="form-group">
                    <label for="email">البريد الإلكتروني:</label>
                    <input type="email" id="email" name="email" required placeholder="أدخل بريدك الإلكتروني">
                </div>

                <button type="submit" class="login-btn">
                    <i class="fas fa-paper-plane"></i>
                    إرسال كود التحقق
                </button>
            </form>
        </div>

        <!-- مرحلة إدخال الكود -->
        <div id="codeStep" style="display: none;">
            <div class="info-message">
                <i class="fas fa-info-circle"></i>
                <p>تم إرسال كود التحقق إلى بريدك الإلكتروني</p>
                <p class="email-display" id="emailDisplay"></p>
            </div>

            <form onsubmit="verifyCode(event)">
                <div class="form-group">
                    <label for="verificationCode">كود التحقق:</label>
                    <input type="text" id="verificationCode" name="verificationCode" required
                           placeholder="أدخل الكود المرسل" maxlength="6" pattern="[0-9]{6}">
                </div>

                <button type="submit" class="login-btn">
                    <i class="fas fa-check"></i>
                    تحقق ودخول
                </button>
            </form>

            <div class="resend-section">
                <p>لم يصلك الكود؟</p>
                <button type="button" class="resend-btn" onclick="resendCode()">
                    <i class="fas fa-redo"></i>
                    إعادة إرسال
                </button>
            </div>
        </div>
    </div>

    <script>
        let userEmail = '';
        let generatedCode = '';
        let resendTimer = 0;

        function goBack() {
            window.location.href = 'index.html';
        }

        function sendVerificationCode(event) {
            event.preventDefault();

            const email = document.getElementById('email').value;
            userEmail = email;

            // توليد كود عشوائي من 6 أرقام
            generatedCode = Math.floor(100000 + Math.random() * 900000).toString();

            // محاكاة إرسال الإيميل (في التطبيق الحقيقي ستحتاج خدمة إيميل)
            console.log(`كود التحقق لـ ${email}: ${generatedCode}`);

            // إظهار رسالة تأكيد
            alert(`تم إرسال كود التحقق إلى: ${email}\n\nالكود هو: ${generatedCode}\n(في التطبيق الحقيقي سيتم إرساله بالإيميل)`);

            // إخفاء مرحلة الإيميل وإظهار مرحلة الكود
            document.getElementById('emailStep').style.display = 'none';
            document.getElementById('codeStep').style.display = 'block';
            document.getElementById('emailDisplay').textContent = email;

            // بدء عداد إعادة الإرسال
            startResendTimer();
        }

        function verifyCode(event) {
            event.preventDefault();

            const enteredCode = document.getElementById('verificationCode').value;

            if (enteredCode === generatedCode) {
                // حفظ بيانات المستخدم
                localStorage.setItem('userEmail', userEmail);
                localStorage.setItem('isLoggedIn', 'true');

                alert('تم التحقق بنجاح! مرحباً بك في ملكوتك آتٍ');

                // التوجيه للصفحة الرئيسية
                window.location.href = 'dashboard.html';
            } else {
                alert('كود التحقق غير صحيح. يرجى المحاولة مرة أخرى.');
                document.getElementById('verificationCode').value = '';
            }
        }

        function resendCode() {
            if (resendTimer > 0) return;

            // توليد كود جديد
            generatedCode = Math.floor(100000 + Math.random() * 900000).toString();

            console.log(`كود التحقق الجديد لـ ${userEmail}: ${generatedCode}`);
            alert(`تم إرسال كود جديد إلى: ${userEmail}\n\nالكود الجديد هو: ${generatedCode}`);

            // بدء عداد إعادة الإرسال
            startResendTimer();
        }

        function startResendTimer() {
            resendTimer = 60; // 60 ثانية
            const resendBtn = document.querySelector('.resend-btn');

            const timer = setInterval(() => {
                if (resendTimer > 0) {
                    resendBtn.textContent = `إعادة إرسال (${resendTimer}s)`;
                    resendBtn.disabled = true;
                    resendTimer--;
                } else {
                    resendBtn.innerHTML = '<i class="fas fa-redo"></i> إعادة إرسال';
                    resendBtn.disabled = false;
                    clearInterval(timer);
                }
            }, 1000);
        }

        // التحقق من تسجيل الدخول عند تحميل الصفحة
        window.onload = function() {
            if (localStorage.getItem('isLoggedIn') === 'true') {
                window.location.href = 'dashboard.html';
            }
        }
    </script>
</body>
</html>
