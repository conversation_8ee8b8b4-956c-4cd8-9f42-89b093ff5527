<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - ملكوتك آتٍ</title>
    <link rel="icon" type="image/svg+xml" href="public/logo.svg">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Sign-In -->
    <script src="https://accounts.google.com/gsi/client" async defer></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-gold: #D4AF37;
            --primary-dark: #B8860B;
            --secondary-blue: #1E3A8A;
            --text-dark: #1F2937;
            --text-light: #6B7280;
            --white: #FFFFFF;
            --shadow: rgba(0, 0, 0, 0.1);
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            direction: rtl;
        }

        .login-container {
            background: var(--white);
            border-radius: 24px;
            padding: 40px 30px;
            box-shadow: 0 20px 40px var(--shadow);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }

        .back-button {
            position: absolute;
            top: 20px;
            right: 20px;
            background: var(--primary-gold);
            color: var(--white);
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            font-size: 18px;
            transition: all 0.3s ease;
        }

        .back-button:hover {
            background: var(--primary-dark);
            transform: scale(1.1);
        }

        .logo {
            width: 60px;
            height: 60px;
            margin: 0 auto 20px;
            background: linear-gradient(135deg, var(--primary-gold), var(--primary-dark));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logo i {
            font-size: 30px;
            color: var(--white);
        }

        .login-title {
            font-family: 'Amiri', serif;
            font-size: 1.8rem;
            color: var(--secondary-blue);
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: right;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: var(--text-dark);
            font-weight: 600;
        }

        .form-group input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
            direction: rtl;
            text-align: right;
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--primary-gold);
        }

        .login-btn {
            width: 100%;
            background: linear-gradient(135deg, var(--primary-gold), var(--primary-dark));
            color: var(--white);
            border: none;
            border-radius: 12px;
            padding: 15px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 10px;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(212, 175, 55, 0.4);
        }

        .forgot-password {
            margin-top: 20px;
            color: var(--text-light);
            text-decoration: none;
            font-size: 0.9rem;
        }

        .forgot-password:hover {
            color: var(--primary-gold);
        }

        .info-message {
            background: linear-gradient(135deg, #e0f2fe, #b3e5fc);
            border: 1px solid #29b6f6;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
            text-align: center;
        }

        .info-message i {
            font-size: 24px;
            color: #1976d2;
            margin-bottom: 10px;
        }

        .info-message p {
            margin: 5px 0;
            color: #1565c0;
        }

        .email-display {
            font-weight: 600;
            color: var(--primary-gold) !important;
            font-size: 0.95rem;
        }

        .resend-section {
            margin-top: 20px;
            text-align: center;
        }

        .resend-section p {
            color: var(--text-light);
            font-size: 0.9rem;
            margin-bottom: 10px;
        }

        .resend-btn {
            background: transparent;
            color: var(--primary-gold);
            border: 2px solid var(--primary-gold);
            border-radius: 8px;
            padding: 8px 16px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .resend-btn:hover {
            background: var(--primary-gold);
            color: var(--white);
        }

        .resend-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        #verificationCode {
            text-align: center;
            font-size: 1.2rem;
            letter-spacing: 2px;
            font-weight: 600;
        }

        .toast-message {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 10px;
            animation: slideInRight 0.3s ease-out;
            max-width: 400px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .toast-message.success {
            background: linear-gradient(135deg, #10b981, #059669);
        }

        .toast-message.error {
            background: linear-gradient(135deg, #ef4444, #dc2626);
        }

        .toast-message i {
            font-size: 18px;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .google-login-section {
            margin-bottom: 25px;
        }

        .google-signin-container {
            width: 100%;
            margin-bottom: 20px;
            display: flex;
            justify-content: center;
        }

        .google-signin-container > div {
            width: 100% !important;
        }

        .google-signin-container iframe {
            width: 100% !important;
        }

        .divider {
            position: relative;
            text-align: center;
            margin: 20px 0;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e5e7eb;
        }

        .divider span {
            background: var(--white);
            padding: 0 15px;
            color: var(--text-light);
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <button class="back-button" onclick="goBack()">
        <i class="fas fa-arrow-right"></i>
    </button>

    <div class="login-container">
        <div class="logo">
            <i class="fas fa-cross"></i>
        </div>
        
        <h2 class="login-title">تسجيل الدخول - ملكوتك آتٍ</h2>

        <!-- تسجيل الدخول بجوجل -->
        <div class="google-login-section">
            <div id="google-signin-button" class="google-signin-container"></div>

            <div class="divider">
                <span>أو</span>
            </div>
        </div>

        <!-- مرحلة إدخال الإيميل -->
        <div id="emailStep">
            <form onsubmit="sendVerificationCode(event)">
                <div class="form-group">
                    <label for="email">البريد الإلكتروني:</label>
                    <input type="email" id="email" name="email" required placeholder="أدخل بريدك الإلكتروني">
                </div>

                <button type="submit" class="login-btn">
                    <i class="fas fa-paper-plane"></i>
                    إرسال كود التحقق
                </button>
            </form>
        </div>

        <!-- مرحلة إدخال الكود -->
        <div id="codeStep" style="display: none;">
            <div class="info-message">
                <i class="fas fa-info-circle"></i>
                <p>تم إرسال كود التحقق إلى بريدك الإلكتروني</p>
                <p class="email-display" id="emailDisplay"></p>
            </div>

            <form onsubmit="verifyCode(event)">
                <div class="form-group">
                    <label for="verificationCode">كود التحقق:</label>
                    <input type="text" id="verificationCode" name="verificationCode" required
                           placeholder="أدخل الكود المرسل" maxlength="6" pattern="[0-9]{6}">
                </div>

                <button type="submit" class="login-btn">
                    <i class="fas fa-check"></i>
                    تحقق ودخول
                </button>
            </form>

            <div class="resend-section">
                <p>لم يصلك الكود؟</p>
                <button type="button" class="resend-btn" onclick="resendCode()">
                    <i class="fas fa-redo"></i>
                    إعادة إرسال
                </button>
            </div>
        </div>
    </div>

    <script>
        let userEmail = '';
        let generatedCode = '';
        let resendTimer = 0;

        // إعداد Google Sign-In
        window.onload = function() {
            // التحقق من تسجيل الدخول المسبق
            if (localStorage.getItem('isLoggedIn') === 'true') {
                window.location.href = 'dashboard.html';
                return;
            }

            // تهيئة Google Sign-In
            google.accounts.id.initialize({
                client_id: '*************-9sl2r2i0dn5s1bqkvgqvb5bd4l6rqssm.apps.googleusercontent.com',
                callback: handleGoogleSignIn,
                auto_select: false,
                cancel_on_tap_outside: false
            });

            // عرض زر Google Sign-In
            google.accounts.id.renderButton(
                document.getElementById('google-signin-button'),
                {
                    theme: 'outline',
                    size: 'large',
                    text: 'signin_with',
                    shape: 'rectangular',
                    logo_alignment: 'left',
                    width: '100%'
                }
            );
        }

        function goBack() {
            window.location.href = 'index.html';
        }

        // لا نحتاج دالة signInWithGoogle منفصلة لأن الزر سيتم إنشاؤه تلقائياً

        function handleGoogleSignIn(response) {
            try {
                // فك تشفير JWT token من جوجل
                const payload = JSON.parse(atob(response.credential.split('.')[1]));
                const userData = {
                    email: payload.email,
                    name: payload.name,
                    picture: payload.picture,
                    given_name: payload.given_name,
                    family_name: payload.family_name
                };

                console.log('بيانات المستخدم من جوجل:', userData);

                // حفظ بيانات المستخدم
                localStorage.setItem('userEmail', userData.email);
                localStorage.setItem('userName', userData.name);
                localStorage.setItem('userPicture', userData.picture);
                localStorage.setItem('isLoggedIn', 'true');
                localStorage.setItem('loginMethod', 'google');

                // إظهار رسالة ترحيب
                showSuccessMessage(`مرحباً ${userData.name}! تم تسجيل الدخول بنجاح في ملكوتك آتٍ`);

                // التوجيه للصفحة الرئيسية بعد ثانيتين
                setTimeout(() => {
                    window.location.href = 'dashboard.html';
                }, 2000);

            } catch (error) {
                console.error('خطأ في تسجيل الدخول بجوجل:', error);
                showErrorMessage('حدث خطأ في تسجيل الدخول بجوجل. يرجى المحاولة مرة أخرى.');
            }
        }

        async function sendVerificationCode(event) {
            event.preventDefault();

            const email = document.getElementById('email').value;
            const submitBtn = event.target.querySelector('button');

            // تعطيل الزر أثناء الإرسال
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإرسال...';

            userEmail = email;

            // توليد كود عشوائي من 6 أرقام
            generatedCode = Math.floor(100000 + Math.random() * 900000).toString();

            // إنشاء رسالة الإيميل
            const emailContent = createEmailContent(generatedCode, email);

            try {
                // إرسال الإيميل باستخدام Formspree
                const response = await fetch('https://formspree.io/f/xpznvqko', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: email,
                        subject: 'كود التحقق - ملكوتك آتٍ',
                        message: emailContent,
                        _replyto: email,
                        _subject: 'كود التحقق - ملكوتك آتٍ'
                    })
                });

                if (response.ok) {
                    console.log('تم إرسال الإيميل بنجاح!');

                    // إظهار رسالة نجاح
                    showSuccessMessage('تم إرسال كود التحقق بنجاح إلى بريدك الإلكتروني');

                    // إخفاء مرحلة الإيميل وإظهار مرحلة الكود
                    document.getElementById('emailStep').style.display = 'none';
                    document.getElementById('codeStep').style.display = 'block';
                    document.getElementById('emailDisplay').textContent = email;

                    // بدء عداد إعادة الإرسال
                    startResendTimer();

                } else {
                    throw new Error('فشل في إرسال الإيميل');
                }

            } catch (error) {
                console.log('فشل في إرسال الإيميل...', error);
                showErrorMessage('حدث خطأ في إرسال الكود. يرجى المحاولة مرة أخرى.');
            } finally {
                // إعادة تفعيل الزر
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-paper-plane"></i> إرسال كود التحقق';
            }
        }

        function createEmailContent(code, email) {
            return `
🌟 مرحباً بك في ملكوتك آتٍ - Kingdom Coming 🌟

السلام عليكم ورحمة الله وبركاته،

نحن سعداء بانضمامك إلى عائلة ملكوتك آتٍ الروحية المباركة.

🔐 كود التحقق الخاص بك هو: ${code}

⏰ هذا الكود صالح لمدة 10 دقائق فقط من وقت الإرسال.

📱 يرجى إدخال هذا الكود في التطبيق لإكمال عملية تسجيل الدخول.

✝️ "سِرَاجٌ لِرِجْلِي كَلاَمُكَ وَنُورٌ لِسَبِيلِي" - مزمور 119: 105

🙏 إذا لم تطلب هذا الكود، يرجى تجاهل هذه الرسالة.

نسأل الله أن يبارك رحلتك الروحية معنا.

مع محبتنا وصلواتنا،
فريق ملكوتك آتٍ

---
تصميم وتطوير: بيشوي مراد
© 2025 جميع الحقوق محفوظة
            `.trim();
        }

        function verifyCode(event) {
            event.preventDefault();

            const enteredCode = document.getElementById('verificationCode').value;

            if (enteredCode === generatedCode) {
                // حفظ بيانات المستخدم
                localStorage.setItem('userEmail', userEmail);
                localStorage.setItem('isLoggedIn', 'true');

                alert('تم التحقق بنجاح! مرحباً بك في ملكوتك آتٍ');

                // التوجيه للصفحة الرئيسية
                window.location.href = 'dashboard.html';
            } else {
                alert('كود التحقق غير صحيح. يرجى المحاولة مرة أخرى.');
                document.getElementById('verificationCode').value = '';
            }
        }

        async function resendCode() {
            if (resendTimer > 0) return;

            const resendBtn = document.querySelector('.resend-btn');
            resendBtn.disabled = true;
            resendBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإرسال...';

            // توليد كود جديد
            generatedCode = Math.floor(100000 + Math.random() * 900000).toString();

            // إنشاء رسالة الإيميل الجديدة
            const emailContent = createEmailContent(generatedCode, userEmail);

            try {
                // إرسال الكود الجديد
                const response = await fetch('https://formspree.io/f/xpznvqko', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: userEmail,
                        subject: 'كود التحقق الجديد - ملكوتك آتٍ',
                        message: emailContent,
                        _replyto: userEmail,
                        _subject: 'كود التحقق الجديد - ملكوتك آتٍ'
                    })
                });

                if (response.ok) {
                    console.log('تم إرسال الكود الجديد بنجاح!');
                    showSuccessMessage('تم إرسال كود جديد إلى بريدك الإلكتروني');

                    // بدء عداد إعادة الإرسال
                    startResendTimer();
                } else {
                    throw new Error('فشل في إرسال الكود الجديد');
                }

            } catch (error) {
                console.log('فشل في إرسال الكود الجديد...', error);
                showErrorMessage('حدث خطأ في إرسال الكود. يرجى المحاولة مرة أخرى.');

                resendBtn.disabled = false;
                resendBtn.innerHTML = '<i class="fas fa-redo"></i> إعادة إرسال';
            }
        }

        function showSuccessMessage(message) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'toast-message success';
            messageDiv.innerHTML = `
                <i class="fas fa-check-circle"></i>
                <span>${message}</span>
            `;
            document.body.appendChild(messageDiv);

            setTimeout(() => {
                messageDiv.remove();
            }, 4000);
        }

        function showErrorMessage(message) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'toast-message error';
            messageDiv.innerHTML = `
                <i class="fas fa-exclamation-circle"></i>
                <span>${message}</span>
            `;
            document.body.appendChild(messageDiv);

            setTimeout(() => {
                messageDiv.remove();
            }, 4000);
        }

        function startResendTimer() {
            resendTimer = 60; // 60 ثانية
            const resendBtn = document.querySelector('.resend-btn');

            const timer = setInterval(() => {
                if (resendTimer > 0) {
                    resendBtn.textContent = `إعادة إرسال (${resendTimer}s)`;
                    resendBtn.disabled = true;
                    resendTimer--;
                } else {
                    resendBtn.innerHTML = '<i class="fas fa-redo"></i> إعادة إرسال';
                    resendBtn.disabled = false;
                    clearInterval(timer);
                }
            }, 1000);
        }

        // تم نقل window.onload للأعلى مع إعداد Google Sign-In
    </script>
</body>
</html>
