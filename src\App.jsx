import React from 'react'
import { Cross, QrCode } from 'lucide-react'
import './App.css'

function App() {
  const handleLogin = () => {
    // هنا سيتم توجيه المستخدم لصفحة تسجيل الدخول
    alert('سيتم توجيهك لصفحة تسجيل الدخول قريباً...')
  }

  return (
    <div className="app-container">
      <div className="welcome-card">
        {/* اللوجو واسم التطبيق */}
        <div className="logo-container">
          <div className="logo">
            <Cross />
          </div>
          <h1 className="app-title">نور الحياة</h1>
          <p className="app-subtitle">رحلة روحية مميزة</p>
        </div>

        {/* الآية المقدسة */}
        <div className="verse-container">
          <p className="verse-text">
            "سِرَاجٌ لِرِجْلِي كَلاَمُكَ وَنُورٌ لِسَبِيلِي"
          </p>
          <p className="verse-reference">
            (مزمور 119: 105)
          </p>
        </div>

        {/* نص الدعوة */}
        <p className="journey-text">
          ابدأ رحلتك الروحية الآن
        </p>

        {/* زر تسجيل الدخول */}
        <button className="login-button" onClick={handleLogin}>
          🔐 سجّل دخولك للبدء
        </button>

        {/* رمز الاستجابة السريعة */}
        <div className="qr-section">
          <p className="qr-title">امسح الكود للوصول السريع</p>
          <div className="qr-code">
            <QrCode size={60} color="#D4AF37" />
          </div>
        </div>

        {/* التذييل */}
        <div className="footer">
          <p className="designer-credit">
            تصميم: Beshoy Morad
          </p>
          <p className="copyright">
            2025 © جميع الحقوق محفوظة
          </p>
        </div>
      </div>
    </div>
  )
}

export default App
