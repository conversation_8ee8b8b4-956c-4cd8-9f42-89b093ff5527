* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --primary-gold: #D4AF37;
  --primary-dark: #B8860B;
  --secondary-blue: #1E3A8A;
  --light-blue: #3B82F6;
  --text-dark: #1F2937;
  --text-light: #6B7280;
  --background: #FAFAFA;
  --white: #FFFFFF;
  --shadow: rgba(0, 0, 0, 0.1);
  --shadow-hover: rgba(0, 0, 0, 0.15);
}

body {
  font-family: 'Cairo', 'Amiri', sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: var(--text-dark);
  direction: rtl;
  text-align: right;
}

#root {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.app-container {
  width: 100%;
  max-width: 450px;
  margin: 0 auto;
  padding: 20px;
}

.welcome-card {
  background: var(--white);
  border-radius: 24px;
  padding: 40px 30px;
  box-shadow: 0 20px 40px var(--shadow);
  text-align: center;
  position: relative;
  overflow: hidden;
}

.welcome-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-gold), var(--primary-dark));
}

.logo-container {
  margin-bottom: 30px;
}

.logo {
  width: 80px;
  height: 80px;
  margin: 0 auto 20px;
  background: linear-gradient(135deg, var(--primary-gold), var(--primary-dark));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 20px rgba(212, 175, 55, 0.3);
}

.logo svg {
  width: 40px;
  height: 40px;
  color: var(--white);
}

.app-title {
  font-family: 'Amiri', serif;
  font-size: 2.2rem;
  font-weight: 700;
  color: var(--secondary-blue);
  margin-bottom: 10px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.app-subtitle {
  font-size: 0.95rem;
  color: var(--text-light);
  margin-bottom: 35px;
}

.verse-container {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-radius: 16px;
  padding: 25px;
  margin-bottom: 35px;
  border-right: 4px solid var(--primary-gold);
}

.verse-text {
  font-family: 'Amiri', serif;
  font-size: 1.1rem;
  line-height: 1.8;
  color: var(--text-dark);
  margin-bottom: 12px;
  font-weight: 400;
}

.verse-reference {
  font-size: 0.9rem;
  color: var(--text-light);
  font-weight: 600;
}

.journey-text {
  font-size: 1rem;
  color: var(--text-light);
  margin-bottom: 30px;
  font-weight: 500;
}

.login-button {
  width: 100%;
  background: linear-gradient(135deg, var(--primary-gold), var(--primary-dark));
  color: var(--white);
  border: none;
  border-radius: 16px;
  padding: 18px 24px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 20px rgba(212, 175, 55, 0.3);
  font-family: 'Cairo', sans-serif;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 25px rgba(212, 175, 55, 0.4);
}

.login-button:active {
  transform: translateY(0);
}

.qr-section {
  margin: 30px 0;
  padding: 20px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 12px;
  border: 1px dashed var(--primary-gold);
}

.qr-title {
  font-size: 0.9rem;
  color: var(--text-dark);
  margin-bottom: 15px;
  font-weight: 600;
}

.qr-code {
  width: 120px;
  height: 120px;
  margin: 0 auto;
  background: var(--white);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px var(--shadow);
}

.footer {
  margin-top: 35px;
  padding-top: 25px;
  border-top: 1px solid #e5e7eb;
  text-align: center;
}

.designer-credit {
  font-size: 0.85rem;
  color: var(--text-light);
  margin-bottom: 8px;
}

.copyright {
  font-size: 0.8rem;
  color: var(--text-light);
}

@media (max-width: 480px) {
  .app-container {
    padding: 15px;
  }
  
  .welcome-card {
    padding: 30px 20px;
  }
  
  .app-title {
    font-size: 1.8rem;
  }
  
  .verse-text {
    font-size: 1rem;
  }
}
