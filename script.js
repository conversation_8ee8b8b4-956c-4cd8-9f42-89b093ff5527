// تطبيق نور الحياة - JavaScript

// دالة تسجيل الدخول
function handleLogin() {
    // إظهار رسالة ترحيب
    showWelcomeMessage();
    
    // هنا يمكن إضافة كود توجيه المستخدم لصفحة تسجيل الدخول
    setTimeout(() => {
        // مثال على التوجيه (يمكن تغييره حسب الحاجة)
        window.location.href = 'login.html';
    }, 2000);
}

// دالة إظهار رسالة ترحيب
function showWelcomeMessage() {
    // إنشاء عنصر الرسالة
    const messageDiv = document.createElement('div');
    messageDiv.className = 'welcome-message';
    messageDiv.innerHTML = `
        <div class="message-content">
            <i class="fas fa-heart"></i>
            <h3>مرحباً بك في نور الحياة</h3>
            <p>سيتم توجيهك لصفحة تسجيل الدخول...</p>
        </div>
    `;
    
    // إضافة الرسالة للصفحة
    document.body.appendChild(messageDiv);
    
    // إزالة الرسالة بعد 3 ثواني
    setTimeout(() => {
        messageDiv.remove();
    }, 3000);
}

// دالة تحديث الوقت (اختيارية)
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('ar-EG');
    
    // يمكن إضافة عرض الوقت في مكان ما بالصفحة إذا أردت
    console.log('الوقت الحالي:', timeString);
}

// دالة تحميل آية عشوائية (يمكن توسيعها لاحقاً)
function loadRandomVerse() {
    const verses = [
        {
            text: "سِرَاجٌ لِرِجْلِي كَلاَمُكَ وَنُورٌ لِسَبِيلِي",
            reference: "مزمور 119: 105"
        },
        {
            text: "أَنَا هُوَ نُورُ الْعَالَمِ. مَنْ يَتْبَعْنِي فَلاَ يَمْشِي فِي الظُّلْمَةِ بَلْ يَكُونُ لَهُ نُورُ الْحَيَاةِ",
            reference: "يوحنا 8: 12"
        },
        {
            text: "اَلرَّبُّ نُورِي وَخَلاَصِي، مِمَّنْ أَخَافُ؟",
            reference: "مزمور 27: 1"
        }
    ];
    
    return verses[Math.floor(Math.random() * verses.length)];
}

// دالة تغيير الآية (اختيارية)
function changeVerse() {
    const verseContainer = document.querySelector('.verse-container');
    const newVerse = loadRandomVerse();
    
    // تأثير الاختفاء
    verseContainer.style.opacity = '0';
    
    setTimeout(() => {
        // تحديث النص
        document.querySelector('.verse-text').textContent = `"${newVerse.text}"`;
        document.querySelector('.verse-reference').textContent = `(${newVerse.reference})`;
        
        // تأثير الظهور
        verseContainer.style.opacity = '1';
    }, 300);
}

// دالة إضافة تأثيرات تفاعلية
function addInteractiveEffects() {
    // تأثير على اللوجو عند النقر
    const logo = document.querySelector('.logo');
    logo.addEventListener('click', function() {
        this.style.transform = 'scale(1.1)';
        setTimeout(() => {
            this.style.transform = 'scale(1)';
        }, 200);
    });
    
    // تأثير على رمز QR عند النقر
    const qrCode = document.querySelector('.qr-code');
    qrCode.addEventListener('click', function() {
        alert('يمكنك مسح هذا الكود للوصول السريع للتطبيق على هاتفك!');
    });
}

// دالة التحقق من دعم المتصفح
function checkBrowserSupport() {
    // التحقق من دعم CSS Grid
    if (!CSS.supports('display', 'grid')) {
        console.warn('المتصفح لا يدعم CSS Grid بشكل كامل');
    }
    
    // التحقق من دعم Flexbox
    if (!CSS.supports('display', 'flex')) {
        console.warn('المتصفح لا يدعم Flexbox بشكل كامل');
    }
}

// دالة إضافة مستمعي الأحداث
function addEventListeners() {
    // عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        console.log('تم تحميل تطبيق نور الحياة بنجاح');
        checkBrowserSupport();
        addInteractiveEffects();
    });
    
    // عند تغيير حجم النافذة
    window.addEventListener('resize', function() {
        // يمكن إضافة كود للتعامل مع تغيير حجم الشاشة
        console.log('تم تغيير حجم النافذة');
    });
}

// تشغيل مستمعي الأحداث
addEventListeners();

// دالة لإضافة تأثيرات CSS إضافية
function addDynamicStyles() {
    const style = document.createElement('style');
    style.textContent = `
        .welcome-message {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            animation: fadeIn 0.3s ease-in-out;
        }
        
        .message-content {
            background: white;
            padding: 30px;
            border-radius: 16px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            animation: slideUp 0.3s ease-out;
        }
        
        .message-content i {
            font-size: 48px;
            color: #D4AF37;
            margin-bottom: 15px;
        }
        
        .message-content h3 {
            color: #1E3A8A;
            margin-bottom: 10px;
            font-family: 'Amiri', serif;
        }
        
        .message-content p {
            color: #6B7280;
            font-size: 0.9rem;
        }
    `;
    document.head.appendChild(style);
}

// إضافة الأنماط الديناميكية
addDynamicStyles();
